using DongleKeyManager.Core.Entities;
using System.ComponentModel.DataAnnotations;

namespace DongleKeyManager.Application.DongleDevice.Dtos;

/// <summary>
/// 设备信息DTO
/// </summary>
public class DongleDeviceDto
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 设备序列号
    /// </summary>
    public string SerialNumber { get; set; } = string.Empty;

    /// <summary>
    /// 硬件唯一标识号
    /// </summary>
    public string HardwareUniqueId { get; set; } = string.Empty;

    /// <summary>
    /// 加密字符串
    /// </summary>
    public string EncryptedString { get; set; } = string.Empty;

    /// <summary>
    /// 设备状态
    /// </summary>
    public DeviceStatus Status { get; set; }

    /// <summary>
    /// 设备状态描述
    /// </summary>
    public string StatusDescription { get; set; } = string.Empty;

    /// <summary>
    /// 客户名称
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// 客户地址
    /// </summary>
    public string? CustomerAddress { get; set; }

    /// <summary>
    /// 授权人名称
    /// </summary>
    public string? AuthorizerName { get; set; }

    /// <summary>
    /// 设备描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdatedTime { get; set; }

    /// <summary>
    /// 激活时间
    /// </summary>
    public DateTime? ActivatedTime { get; set; }

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime? LastUsedTime { get; set; }

    /// <summary>
    /// 使用次数
    /// </summary>
    public int UsageCount { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remarks { get; set; }
}

/// <summary>
/// 创建设备DTO
/// </summary>
public class CreateDongleDeviceDto
{
    /// <summary>
    /// 硬件唯一标识号
    /// </summary>
    [Required(ErrorMessage = "硬件唯一标识号不能为空")]
    [StringLength(200, ErrorMessage = "硬件唯一标识号长度不能超过200个字符")]
    public string HardwareUniqueId { get; set; } = string.Empty;

    /// <summary>
    /// 客户名称
    /// </summary>
    [StringLength(200, ErrorMessage = "客户名称长度不能超过200个字符")]
    public string? CustomerName { get; set; }

    /// <summary>
    /// 客户地址
    /// </summary>
    [StringLength(500, ErrorMessage = "客户地址长度不能超过500个字符")]
    public string? CustomerAddress { get; set; }

    /// <summary>
    /// 授权人名称
    /// </summary>
    [StringLength(100, ErrorMessage = "授权人名称长度不能超过100个字符")]
    public string? AuthorizerName { get; set; }

    /// <summary>
    /// 设备描述
    /// </summary>
    [StringLength(500, ErrorMessage = "设备描述长度不能超过500个字符")]
    public string? Description { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
    public string? Remarks { get; set; }
}

/// <summary>
/// 更新设备DTO
/// </summary>
public class UpdateDongleDeviceDto
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    public long Id { get; set; }

    /// <summary>
    /// 硬件唯一标识号
    /// </summary>
    [Required(ErrorMessage = "硬件唯一标识号不能为空")]
    [StringLength(200, ErrorMessage = "硬件唯一标识号长度不能超过200个字符")]
    public string HardwareUniqueId { get; set; } = string.Empty;

    /// <summary>
    /// 客户名称
    /// </summary>
    [StringLength(200, ErrorMessage = "客户名称长度不能超过200个字符")]
    public string? CustomerName { get; set; }

    /// <summary>
    /// 客户地址
    /// </summary>
    [StringLength(500, ErrorMessage = "客户地址长度不能超过500个字符")]
    public string? CustomerAddress { get; set; }

    /// <summary>
    /// 授权人名称
    /// </summary>
    [StringLength(100, ErrorMessage = "授权人名称长度不能超过100个字符")]
    public string? AuthorizerName { get; set; }

    /// <summary>
    /// 设备描述
    /// </summary>
    [StringLength(500, ErrorMessage = "设备描述长度不能超过500个字符")]
    public string? Description { get; set; }

    /// <summary>
    /// 设备状态
    /// </summary>
    public DeviceStatus Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
    public string? Remarks { get; set; }
}

/// <summary>
/// 设备查询DTO
/// </summary>
public class QueryDongleDeviceDto
{
    /// <summary>
    /// 设备序列号（模糊查询）
    /// </summary>
    public string? SerialNumber { get; set; }

    /// <summary>
    /// 硬件唯一标识号（模糊查询）
    /// </summary>
    public string? HardwareUniqueId { get; set; }

    /// <summary>
    /// 客户名称（模糊查询）
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// 设备状态
    /// </summary>
    public DeviceStatus? Status { get; set; }

    /// <summary>
    /// 创建开始时间
    /// </summary>
    public DateTime? CreatedStartTime { get; set; }

    /// <summary>
    /// 创建结束时间
    /// </summary>
    public DateTime? CreatedEndTime { get; set; }

    /// <summary>
    /// 页码
    /// </summary>
    [Range(1, long.MaxValue, ErrorMessage = "页码必须大于0")]
    public int PageIndex { get; set; } = 1;

    /// <summary>
    /// 页大小
    /// </summary>
    [Range(1, 100, ErrorMessage = "页大小必须在1-100之间")]
    public int PageSize { get; set; } = 20;
}

/// <summary>
/// 设备验证DTO
/// </summary>
public class VerifyDongleDeviceDto
{
    /// <summary>
    /// 硬件唯一标识号
    /// </summary>
    [Required(ErrorMessage = "硬件唯一标识号不能为空")]
    public string HardwareUniqueId { get; set; } = string.Empty;

    /// <summary>
    /// 加密字符串
    /// </summary>
    [Required(ErrorMessage = "加密字符串不能为空")]
    public string EncryptedString { get; set; } = string.Empty;
}

/// <summary>
/// 设备验证结果DTO
/// </summary>
public class DeviceVerificationResultDto
{
    /// <summary>
    /// 验证是否成功
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public DongleDeviceDto? Device { get; set; }

    /// <summary>
    /// 验证消息
    /// </summary>
    public string Message { get; set; } = string.Empty;
}
