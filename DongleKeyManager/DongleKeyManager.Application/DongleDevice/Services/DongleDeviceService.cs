using DongleKeyManager.Application.DongleDevice.Dtos;
using DongleKeyManager.Application.Services;
using DongleKeyManager.Core;
using DongleKeyManager.Core.Entities;

namespace DongleKeyManager.Application.DongleDevice.Services;

/// <summary>
/// 设备管理服务实现
/// </summary>
public class DongleDeviceService : IDongleDeviceService, ITransient
{
    private readonly IEncryptionService _encryptionService;

    public DongleDeviceService(IEncryptionService encryptionService)
    {
        _encryptionService = encryptionService;
    }

    /// <summary>
    /// 创建设备
    /// </summary>
    public async Task<DongleDeviceDto> CreateAsync(CreateDongleDeviceDto dto, long? userId = null)
    {
        // 检查硬件唯一标识号是否已存在
        if (await IsHardwareUniqueIdExistsAsync(dto.HardwareUniqueId))
        {
            throw Oops.Bah($"硬件唯一标识号 {dto.HardwareUniqueId} 已存在");
        }

        // 自动生成序列号
        var serialNumber = await GenerateSerialNumberAsync();

        // 生成加密字符串
        var encryptedString = _encryptionService.GenerateDeviceEncryptedString(dto.HardwareUniqueId);

        var entity = new Core.Entities.DongleDevice
        {
            SerialNumber = serialNumber,
            HardwareUniqueId = dto.HardwareUniqueId,
            EncryptedString = encryptedString,
            CustomerName = dto.CustomerName,
            CustomerAddress = dto.CustomerAddress,
            AuthorizerName = dto.AuthorizerName,
            Description = dto.Description,
            Remarks = dto.Remarks,
            CreatedUserId = userId,
            CreatedTime = DateTime.Now
        };

        var result = await DbContext.Instance.Insertable(entity).ExecuteReturnEntityAsync();

        // 记录日志
        await LogDeviceOperationAsync(result.Id, LogOperationType.Create, "创建设备", userId);

        return await MapToDto(result);
    }

    /// <summary>
    /// 更新设备
    /// </summary>
    public async Task<DongleDeviceDto> UpdateAsync(UpdateDongleDeviceDto dto, long? userId = null)
    {
        var entity = await DbContext.Instance.Queryable<Core.Entities.DongleDevice>()
            .Where(x => x.Id == dto.Id && !x.IsDeleted)
            .FirstAsync();

        if (entity == null)
        {
            throw Oops.Bah("设备不存在");
        }

        // 检查硬件唯一标识号是否已存在（排除当前设备）
        if (await IsHardwareUniqueIdExistsAsync(dto.HardwareUniqueId, dto.Id))
        {
            throw Oops.Bah($"硬件唯一标识号 {dto.HardwareUniqueId} 已存在");
        }

        // 如果硬件唯一标识号发生变化，重新生成加密字符串
        var encryptedString = entity.EncryptedString;
        if (entity.HardwareUniqueId != dto.HardwareUniqueId)
        {
            encryptedString = _encryptionService.GenerateDeviceEncryptedString(dto.HardwareUniqueId);
        }

        entity.HardwareUniqueId = dto.HardwareUniqueId;
        entity.EncryptedString = encryptedString;
        entity.CustomerName = dto.CustomerName;
        entity.CustomerAddress = dto.CustomerAddress;
        entity.AuthorizerName = dto.AuthorizerName;
        entity.Description = dto.Description;
        entity.Status = dto.Status;
        entity.Remarks = dto.Remarks;
        entity.UpdatedUserId = userId;
        entity.UpdatedTime = DateTime.Now;

        await DbContext.Instance.Updateable(entity).ExecuteCommandAsync();

        // 记录日志
        await LogDeviceOperationAsync(entity.Id, LogOperationType.Update, "更新设备", userId);

        return await MapToDto(entity);
    }

    /// <summary>
    /// 删除设备
    /// </summary>
    public async Task<bool> DeleteAsync(long id, long? userId = null)
    {
        var entity = await DbContext.Instance.Queryable<Core.Entities.DongleDevice>()
            .Where(x => x.Id == id && !x.IsDeleted)
            .FirstAsync();

        if (entity == null)
        {
            return false;
        }

        entity.IsDeleted = true;
        entity.UpdatedUserId = userId;
        entity.UpdatedTime = DateTime.Now;

        var result = await DbContext.Instance.Updateable(entity).ExecuteCommandAsync() > 0;

        if (result)
        {
            // 记录日志
            await LogDeviceOperationAsync(id, LogOperationType.Delete, "删除设备", userId);
        }

        return result;
    }

    /// <summary>
    /// 根据ID获取设备
    /// </summary>
    public async Task<DongleDeviceDto?> GetByIdAsync(long id)
    {
        var entity = await DbContext.Instance.Queryable<Core.Entities.DongleDevice>()
            .Where(x => x.Id == id && !x.IsDeleted)
            .FirstAsync();

        return entity == null ? null : await MapToDto(entity);
    }

    /// <summary>
    /// 根据序列号获取设备
    /// </summary>
    public async Task<DongleDeviceDto?> GetBySerialNumberAsync(string serialNumber)
    {
        var entity = await DbContext.Instance.Queryable<Core.Entities.DongleDevice>()
            .Where(x => x.SerialNumber == serialNumber && !x.IsDeleted)
            .FirstAsync();

        return entity == null ? null : await MapToDto(entity);
    }

    /// <summary>
    /// 根据硬件唯一标识号获取设备
    /// </summary>
    public async Task<DongleDeviceDto?> GetByHardwareUniqueIdAsync(string hardwareUniqueId)
    {
        var entity = await DbContext.Instance.Queryable<Core.Entities.DongleDevice>()
            .Where(x => x.HardwareUniqueId == hardwareUniqueId && !x.IsDeleted)
            .FirstAsync();

        return entity == null ? null : await MapToDto(entity);
    }

    /// <summary>
    /// 分页查询设备
    /// </summary>
    public async Task<(List<DongleDeviceDto> Items, int TotalCount)> GetPagedListAsync(QueryDongleDeviceDto dto)
    {
        var query = DbContext.Instance.Queryable<Core.Entities.DongleDevice>()
            .Where(x => !x.IsDeleted);

        // 添加查询条件
        if (!string.IsNullOrEmpty(dto.SerialNumber))
        {
            query = query.Where(x => x.SerialNumber.Contains(dto.SerialNumber));
        }

        if (!string.IsNullOrEmpty(dto.HardwareUniqueId))
        {
            query = query.Where(x => x.HardwareUniqueId.Contains(dto.HardwareUniqueId));
        }

        if (!string.IsNullOrEmpty(dto.CustomerName))
        {
            query = query.Where(x => x.CustomerName != null && x.CustomerName.Contains(dto.CustomerName));
        }

        if (dto.Status.HasValue)
        {
            query = query.Where(x => x.Status == dto.Status.Value);
        }

        if (dto.CreatedStartTime.HasValue)
        {
            query = query.Where(x => x.CreatedTime >= dto.CreatedStartTime.Value);
        }

        if (dto.CreatedEndTime.HasValue)
        {
            query = query.Where(x => x.CreatedTime <= dto.CreatedEndTime.Value);
        }

        // 获取总数
        var totalCount = await query.CountAsync();

        // 分页查询
        var entities = await query
            .OrderByDescending(x => x.CreatedTime)
            .Skip((dto.PageIndex - 1) * dto.PageSize)
            .Take(dto.PageSize)
            .ToListAsync();

        var items = new List<DongleDeviceDto>();
        foreach (var entity in entities)
        {
            items.Add(await MapToDto(entity));
        }

        return (items, totalCount);
    }

    /// <summary>
    /// 激活设备
    /// </summary>
    public async Task<bool> ActivateAsync(long id, long? userId = null)
    {
        var entity = await DbContext.Instance.Queryable<Core.Entities.DongleDevice>()
            .Where(x => x.Id == id && !x.IsDeleted)
            .FirstAsync();

        if (entity == null)
        {
            return false;
        }

        entity.Status = DeviceStatus.Active;
        entity.ActivatedTime = DateTime.Now;
        entity.UpdatedUserId = userId;
        entity.UpdatedTime = DateTime.Now;

        var result = await DbContext.Instance.Updateable(entity).ExecuteCommandAsync() > 0;

        if (result)
        {
            // 记录日志
            await LogDeviceOperationAsync(id, LogOperationType.Activate, "激活设备", userId);
        }

        return result;
    }

    /// <summary>
    /// 禁用设备
    /// </summary>
    public async Task<bool> DisableAsync(long id, long? userId = null)
    {
        var entity = await DbContext.Instance.Queryable<Core.Entities.DongleDevice>()
            .Where(x => x.Id == id && !x.IsDeleted)
            .FirstAsync();

        if (entity == null)
        {
            return false;
        }

        entity.Status = DeviceStatus.Disabled;
        entity.UpdatedUserId = userId;
        entity.UpdatedTime = DateTime.Now;

        var result = await DbContext.Instance.Updateable(entity).ExecuteCommandAsync() > 0;

        if (result)
        {
            // 记录日志
            await LogDeviceOperationAsync(id, LogOperationType.Disable, "禁用设备", userId);
        }

        return result;
    }

    /// <summary>
    /// 验证设备
    /// </summary>
    public async Task<DeviceVerificationResultDto> VerifyAsync(VerifyDongleDeviceDto dto, long? userId = null)
    {
        var result = new DeviceVerificationResultDto();

        try
        {
            // 根据硬件唯一标识号查找设备
            var entity = await DbContext.Instance.Queryable<Core.Entities.DongleDevice>()
                .Where(x => x.HardwareUniqueId == dto.HardwareUniqueId && !x.IsDeleted)
                .FirstAsync();

            if (entity == null)
            {
                result.IsValid = false;
                result.Message = "设备不存在";
                return result;
            }

            // 验证加密字符串
            if (!_encryptionService.VerifyDeviceEncryptedString(dto.HardwareUniqueId, dto.EncryptedString))
            {
                result.IsValid = false;
                result.Message = "加密字符串验证失败";
                
                // 记录验证失败日志
                await LogDeviceOperationAsync(entity.Id, LogOperationType.Verify, "设备验证失败：加密字符串不匹配", userId, false, "加密字符串验证失败");
                return result;
            }

            // 检查设备状态
            if (entity.Status != DeviceStatus.Active)
            {
                result.IsValid = false;
                result.Message = $"设备状态异常：{GetStatusDescription(entity.Status)}";
                
                // 记录验证失败日志
                await LogDeviceOperationAsync(entity.Id, LogOperationType.Verify, $"设备验证失败：设备状态为{GetStatusDescription(entity.Status)}", userId, false, result.Message);
                return result;
            }

            // 注意：已移除过期时间检查，因为不再使用ExpiryTime字段

            // 验证成功，更新使用记录
            entity.LastUsedTime = DateTime.Now;
            entity.UsageCount++;
            entity.UpdatedTime = DateTime.Now;
            await DbContext.Instance.Updateable(entity).ExecuteCommandAsync();

            result.IsValid = true;
            result.Message = "设备验证成功";
            result.Device = await MapToDto(entity);

            // 记录验证成功日志
            await LogDeviceOperationAsync(entity.Id, LogOperationType.Verify, "设备验证成功", userId, true);

            return result;
        }
        catch (Exception ex)
        {
            result.IsValid = false;
            result.Message = "验证过程中发生错误";
            
            // 记录异常日志
            await LogDeviceOperationAsync(0, LogOperationType.Verify, "设备验证异常", userId, false, ex.Message);
            
            throw;
        }
    }

    /// <summary>
    /// 检查序列号是否存在
    /// </summary>
    public async Task<bool> IsSerialNumberExistsAsync(string serialNumber, long? excludeId = null)
    {
        var query = DbContext.Instance.Queryable<Core.Entities.DongleDevice>()
            .Where(x => x.SerialNumber == serialNumber && !x.IsDeleted);

        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        return await query.AnyAsync();
    }

    /// <summary>
    /// 检查硬件唯一标识号是否存在
    /// </summary>
    public async Task<bool> IsHardwareUniqueIdExistsAsync(string hardwareUniqueId, long? excludeId = null)
    {
        var query = DbContext.Instance.Queryable<Core.Entities.DongleDevice>()
            .Where(x => x.HardwareUniqueId == hardwareUniqueId && !x.IsDeleted);

        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        return await query.AnyAsync();
    }

    /// <summary>
    /// 获取设备统计信息
    /// </summary>
    public async Task<DeviceStatisticsDto> GetStatisticsAsync()
    {
        var query = DbContext.Instance.Queryable<Core.Entities.DongleDevice>()
            .Where(x => !x.IsDeleted);

        var totalCount = await query.CountAsync();
        var activeCount = await query.Where(x => x.Status == DeviceStatus.Active).CountAsync();
        var inactiveCount = await query.Where(x => x.Status == DeviceStatus.Inactive).CountAsync();
        var disabledCount = await query.Where(x => x.Status == DeviceStatus.Disabled).CountAsync();
        var expiredCount = await query.Where(x => x.Status == DeviceStatus.Expired).CountAsync();

        var today = DateTime.Today;
        var todayAddedCount = await query.Where(x => x.CreatedTime >= today).CountAsync();

        var monthStart = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
        var monthAddedCount = await query.Where(x => x.CreatedTime >= monthStart).CountAsync();

        return new DeviceStatisticsDto
        {
            TotalCount = totalCount,
            ActiveCount = activeCount,
            InactiveCount = inactiveCount,
            DisabledCount = disabledCount,
            ExpiredCount = expiredCount,
            TodayAddedCount = todayAddedCount,
            MonthAddedCount = monthAddedCount
        };
    }

    /// <summary>
    /// 映射到DTO
    /// </summary>
    private async Task<DongleDeviceDto> MapToDto(Core.Entities.DongleDevice entity)
    {
        return new DongleDeviceDto
        {
            Id = entity.Id,
            SerialNumber = entity.SerialNumber,
            HardwareUniqueId = entity.HardwareUniqueId,
            EncryptedString = entity.EncryptedString,
            Status = entity.Status,
            StatusDescription = GetStatusDescription(entity.Status),
            CustomerName = entity.CustomerName,
            CustomerAddress = entity.CustomerAddress,
            AuthorizerName = entity.AuthorizerName,
            Description = entity.Description,
            CreatedTime = entity.CreatedTime,
            UpdatedTime = entity.UpdatedTime,
            ActivatedTime = entity.ActivatedTime,
            LastUsedTime = entity.LastUsedTime,
            UsageCount = entity.UsageCount,
            Remarks = entity.Remarks
        };
    }

    /// <summary>
    /// 获取状态描述
    /// </summary>
    private string GetStatusDescription(DeviceStatus status)
    {
        return status switch
        {
            DeviceStatus.Inactive => "未激活",
            DeviceStatus.Active => "已激活",
            DeviceStatus.Disabled => "已禁用",
            DeviceStatus.Expired => "已过期",
            _ => "未知状态"
        };
    }

    /// <summary>
    /// 生成序列号
    /// </summary>
    private async Task<string> GenerateSerialNumberAsync()
    {
        var today = DateTime.Now.ToString("yyyyMMdd");
        var prefix = $"SN-{today}-";

        // 查询当天已有的最大序号
        var maxSerialNumber = await DbContext.Instance.Queryable<Core.Entities.DongleDevice>()
            .Where(x => x.SerialNumber.StartsWith(prefix) && !x.IsDeleted)
            .OrderByDescending(x => x.SerialNumber)
            .Select(x => x.SerialNumber)
            .FirstAsync();

        int nextNumber = 1;
        if (!string.IsNullOrEmpty(maxSerialNumber))
        {
            // 提取序号部分并递增
            var numberPart = maxSerialNumber.Substring(prefix.Length);
            if (int.TryParse(numberPart, out int currentNumber))
            {
                nextNumber = currentNumber + 1;
            }
        }

        return $"{prefix}{nextNumber:D4}";
    }

    /// <summary>
    /// 记录设备操作日志
    /// </summary>
    private async Task LogDeviceOperationAsync(long deviceId, LogOperationType operationType, string description, long? userId = null, bool isSuccess = true, string? errorMessage = null)
    {
        try
        {
            var log = new DeviceLog
            {
                DeviceId = deviceId,
                OperationType = operationType,
                OperationDescription = description,
                UserId = userId,
                OperationTime = DateTime.Now,
                IsSuccess = isSuccess,
                ErrorMessage = errorMessage
            };

            await DbContext.Instance.Insertable(log).ExecuteCommandAsync();
        }
        catch
        {
            // 日志记录失败不影响主业务流程
        }
    }
}
